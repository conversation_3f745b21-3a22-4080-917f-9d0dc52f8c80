// Get balance
import chalk from "chalk";
import useStore from "../stores.js";

const getCurrentDateTime = (dateOnlyWithSlash = false) => {
    const date = new Date().toLocaleString("en-US", {
        timeZone: "Asia/Ho_Chi_Minh",
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
    });

    const now = new Date(date);

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");

    if (dateOnlyWithSlash) {
        const currentDate = `${day}/${month}/${year}`;
        console.log(chalk.red("Current Datetime:", chalk.green(currentDate)));

        return currentDate;
    }

    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    return `${year}${month}${day}${hours}${minutes}${seconds}00`;
};

export default async function getBalance(
    request,
    reply,
    browser,
    page,
    event,
    body,
    username,
    number,
) {
    console.log(chalk.red("getBalance:", chalk.green(event.request.url)));
    const store = useStore.getState();

    // Skip card request
    if (event.request.hasPostData) {
        const postData = JSON.parse(event.request.postData);

        if (
            postData.hasOwnProperty("type") &&
            postData.type.toLowerCase() === "card"
        ) {
            // console.log(chalk.yellow("Skip for card request"));

            return;
        }
    }

    if (!body.hasOwnProperty("acct_list") || !body.acct_list) {
        await browser.close();
        return reply.code(400).send({
            success: false,
            message: "No account balance found!",
            result: body.result,
        });
    }

    // Get account number
    let foundAccount = null;

    for (const account of body.acct_list) {
        if (account.acctNo === number) {
            foundAccount = account;
            // console.log(chalk.green("Found account balance:"), account);
            store.setAccount(account);
            break;
        }
    }

    if (!foundAccount) {
        await browser.close();
        return reply.code(400).send({
            success: false,
            message: "No account balance found after looped!",
            error: "number",
            result: body.result,
        });
    }

    const refNo = `${username.toUpperCase()}-${getCurrentDateTime()}`;
    const historyPostData = {
        accountNo: number,
        deviceIdCommon: useStore.getState().deviceId,
        fromDate: getCurrentDateTime(true),
        toDate: getCurrentDateTime(true),
        sessionId: useStore.getState().sessionId,
        refNo,
    };

    const lastRequestHeaders = Object.assign(event.request.headers, {
        "X-Request-Id": refNo,
        RefNo: refNo,
    });

    // Request history
    let transactionResponse = {
        result: {
            ok: false,
        },
    };

    try {
        console.log(chalk.red("get-account-transaction-history:", chalk.green("https://online.mbbank.com.vn/api/retail-transactionms/transactionms/get-account-transaction-history")));

        transactionResponse = await page.evaluate(
            async (historyPostData, lastRequestHeaders) => {
                try {
                    const result = await fetch(
                        "https://online.mbbank.com.vn/api/retail-transactionms/transactionms/get-account-transaction-history",
                        {
                            method: "POST",
                            headers: lastRequestHeaders,
                            body: JSON.stringify(historyPostData),
                        },
                    );

                    return await result.json();
                } catch (e) {
                    return {
                        result: {
                            ok: false,
                            message: e.message,
                        },
                    };
                }
            },
            historyPostData,
            lastRequestHeaders,
        );

        // console.log('Transaction history response:', transactionResponse);

        if (!transactionResponse.result.ok) {
            console.log(chalk.red("Cannot get transaction history!"));

            await browser.close();
            return reply.code(400).send({
                success: false,
                message: "Cannot get transaction history!",
                result: body.result,
            });
        }

        store.setTransactions(transactionResponse.transactionHistoryList);
    } catch (e) {
        await browser.close();
        return reply.code(400).send({
            success: false,
            message: "Cannot handle evaluate transaction history!",
            result: body.result,
        });
    }

    // console.log(chalk.green("Transactions:"), useStore.getState().transactions);

    // Logout session
    const logoutRefNo = `${username.toUpperCase()}-${getCurrentDateTime()}`;
    const logoutPostData = {
        sessionId: useStore.getState().sessionId,
        refNo: logoutRefNo,
        deviceIdCommon: useStore.getState().deviceId,
    };

    const logoutRequestHeaders = Object.assign(event.request.headers, {
        "X-Request-Id": logoutRefNo,
        RefNo: logoutRefNo,
    });

    await page.evaluate(
        async (logoutPostData, logoutRequestHeaders) => {
            await fetch(
                "https://online.mbbank.com.vn/api/retail_web/common/doLogout",
                {
                    method: "POST",
                    headers: logoutRequestHeaders,
                    body: JSON.stringify(logoutPostData),
                },
            );
        },
        logoutPostData,
        logoutRequestHeaders,
    );

    await browser.close();

    return reply.send({
        success: true,
        account: useStore.getState().account,
        transactions: useStore.getState().transactions,
        deviceIdCommon: useStore.getState().deviceId,
        sessionId: useStore.getState().sessionId,
    });
}
