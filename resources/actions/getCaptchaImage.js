// Get balance
import chalk from "chalk";
import axios from "axios";

export default async function getCaptchaImage(
  request,
  reply,
  browser,
  page,
  event,
  body,
  username,
  password,
) {
  console.log(chalk.red("Get captcha image:", chalk.green(event.request.url)));

  const logError = (message) => {
    console.log(chalk.red("getCaptchaImage Error:", message));
  };

  if (body.hasOwnProperty("imageString")) {
    // console.log(chalk.green("Found captcha data:"));
    // console.log(chalk.yellow(JSON.stringify(body)));

    // Solve captcha here
    const captcha = await axios
      .post("http://157.10.53.230:8080/mbbank", {
        base64: body.imageString,
      })
      .catch(async (e) => {
        console.log(chalk.red("Error:", e.message));
        logError("Cannot solve captcha!");
        return;
      });

    if (!captcha || !captcha?.data) {
      logError("Cannot solve captcha!");
      return;
    }

    // console.log(chalk.green("Captcha resolved:", chalk.yellow(captcha.data)));

    // Fill username, password and captcha

    try {
      const usernameInput = await page.$('::-p-xpath(//*[@id="user-id"])');
      if (usernameInput) {
        // console.log(chalk.green("Fill usernameInput"));
        await usernameInput.type(username);
      } else {
        logError("Cannot find username input!");
        return;
      }
    } catch (e) {
      console.log(chalk.red("Error:", e.message));
      logError("Cannot find username input!");
      return;
    }

    try {
      const passwordInput = await page.$('::-p-xpath(//*[@id="new-password"])');
      if (passwordInput) {
        // console.log(chalk.green("Fill passwordInput"));
        await passwordInput.type(password);
      } else {
        logError("Cannot find password input!");
        return;
      }
    } catch (e) {
      console.log(chalk.red("Error:", e.message));
      logError("Cannot find password input!");
      return;
    }

    try {
      const captchaInput = await page.$(
        '::-p-xpath(//*[@id="form1"]/div/div[5]/mbb-word-captcha/div/div[2]/div[1]/div[2]/input)',
      );
      if (captchaInput) {
        // console.log(chalk.green("Fill captchaInput"));
        await captchaInput.type(captcha.data);
      } else {
        logError("Cannot find captcha input!");
        return;
      }
    } catch (e) {
      console.log(chalk.red("Error:", e.message));
      logError("Cannot find captcha input!");
      return;
    }

    await new Promise((r) => setTimeout(r, 3000));

    // await page.keyboard.press("Tab");

    // await page.keyboard.press("Enter");
    // await new Promise((r) => setTimeout(r, 3000));
    //
    // await page.keyboard.press("Enter");
    // await new Promise((r) => setTimeout(r, 3000));
    //
    // await page.keyboard.press("Enter");

    let clickedCount = 0;

    const setIntervalId = setInterval(async () => {
      clickedCount++;

      if (clickedCount > 5) {
        clearInterval(setIntervalId);
        logError("Cannot click button to login!");
        return;
      }

      try {
        const loginButton = await page.$('::-p-xpath(//*[@id="login-btn"])', {
          timeout: 15000,
        });

        if (loginButton) {
          console.log(chalk.green("Click loginButton"));
          await loginButton.click();

          // clearInterval(setIntervalId);
        }
      } catch (e) {
        logError(e.message);
        return;
      }
    }, 2000);
  }
}
