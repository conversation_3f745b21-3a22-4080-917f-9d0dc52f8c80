// Get balance
import chalk from "chalk";
import useStore from "../stores.js";

export default async function doLogin(
  request,
  reply,
  browser,
  page,
  event,
  body,
  number,
) {
  console.log(chalk.red("doLogin:", chalk.green(event.request.url)));
  const store = useStore.getState();

  // Wrong account or password
  if (!body.hasOwnProperty("cust") || !body.cust) {
    console.log(chalk.red("Error with account information!"));
    return;
  }

  // Get account number
  let foundAccount = null;

  Object.entries(body.cust.acct_list).forEach(([key, account]) => {
    if (key === number) {
      foundAccount = account;
      // console.log(chalk.green("Found account:"), account);
    }
  });

  if (!foundAccount) {
    console.log(chalk.red("No account found!"));
    return;
  }

  store.setSessionId(body.sessionId);
  store.setDeviceId(body.cust.deviceId);
}
