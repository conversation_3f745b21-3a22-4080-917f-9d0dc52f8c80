import { create } from "zustand";

const useStore = create((set) => ({
  sessionId: null,
  setSessionId: (sessionId) => set(() => ({ sessionId })),
  deviceId: null,
  setDeviceId: (deviceId) => set(() => ({ deviceId })),
  account: {},
  setAccount: (account) => set(() => ({ account })),
  transactions: [],
  setTransactions: (transactions) => set(() => ({ transactions })),
  completed: false,
  setCompleted: (completed) => set(() => ({ completed })),
  // Reset all data
  reset: () => set(() => ({
    sessionId: null,
    deviceId: null,
    account: {},
    transactions: [],
    completed: false,
  })),
}));

export default useStore;
